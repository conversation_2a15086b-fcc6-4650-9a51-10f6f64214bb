# 智能打包临时文件处理方案

## 概述

本文档记录了智能打包功能中临时文件丢失问题的分析和解决方案。该问题主要出现在断点续传场景下，当用户关闭应用后重新打开时，智能打包生成的临时压缩文件可能已被系统清理，导致上传失败。

## 问题描述

### 错误现象
```
Error: ENOENT: no such file or directory, stat '/var/folders/74/ypz2pg996kdb4q105z7g821c0000gn/T/clouddrive-archives/pwa.7z'
```

### 问题场景
1. 用户选择多个文件，触发智能打包上传
2. 系统在临时目录创建压缩文件
3. 上传进行到一半时用户关闭应用
4. 系统或用户清理临时目录
5. 重新打开应用时，临时文件已不存在
6. 断点续传尝试访问不存在的文件，导致ENOENT错误

## 技术分析

### 智能打包流程
```mermaid
graph TD
    A[选择多个文件] --> B[触发智能打包条件]
    B --> C[创建临时压缩文件]
    C --> D[开始上传压缩文件]
    D --> E[应用关闭]
    E --> F[临时文件可能被清理]
    F --> G[重新打开应用]
    G --> H[尝试断点续传]
    H --> I{临时文件存在?}
    I -->|否| J[ENOENT错误]
    I -->|是| K[正常续传]
```

### 临时文件生命周期
1. **创建阶段**：在`/tmp/clouddrive-archives/`目录下创建
2. **使用阶段**：TUS上传过程中持续访问
3. **清理阶段**：
   - 正常完成：上传成功后自动清理
   - 异常中断：可能被系统或用户清理
   - 应用重启：临时文件状态不确定

## 解决方案

### 方案1：临时文件存在性检查（已实现）

在`src/components/upload/composables/useTusUpload.ts`中添加了检查机制：

```typescript
/**
 * 检查智能打包任务的临时文件是否存在
 */
const checkSmartPackedTaskFile = async (task: UploadTask) => {
  try {
    if (isElectron.value) {
      const api = getElectronAPI();
      if (api && api.tus && typeof api.tus.checkFileExists === "function") {
        const fileExists = await api.tus.checkFileExists(task.filePath || "");
        if (!fileExists) {
          tusLogger.warn(`智能打包临时文件不存在: ${task.fileName}`);
          return false;
        }
      }
    }
    return true;
  } catch (error) {
    tusLogger.warn(`检查智能打包文件失败: ${task.fileName}`, error);
    return false;
  }
};
```

**使用方式**：
```typescript
// 在refreshTasks中调用
for (const task of response.tasks) {
  if (task.metadata?.uploadType === "smart-packed-archive" && 
      (task.status === "pending" || task.status === "paused")) {
    const fileExists = await checkSmartPackedTaskFile(task);
    if (!fileExists) {
      // 标记任务为错误状态
      const updatedTask = {
        ...task,
        status: "error" as UploadStatus,
        error: "临时压缩文件已被清理，请重新上传"
      };
      tasks.value.set(task.id, updatedTask);
    }
  }
}
```

### 方案2：主进程API支持（需要实现）

需要在主进程中实现`checkFileExists` API：

```typescript
// electron/tus/ipcHandlers.ts
ipcMain.handle("tus:checkFileExists", async (event, filePath: string) => {
  try {
    const fs = await import("fs/promises");
    await fs.access(filePath);
    return { success: true, exists: true };
  } catch (error) {
    return { success: true, exists: false };
  }
});
```

### 方案3：临时文件持久化（推荐长期方案）

#### 3.1 持久化目录
将临时文件存储在应用数据目录而非系统临时目录：

```typescript
// 使用应用数据目录
const { app } = require('electron');
const persistentTempDir = path.join(app.getPath('userData'), 'temp-archives');
```

#### 3.2 文件生命周期管理
```typescript
class PersistentArchiveManager {
  private archiveDir: string;
  
  constructor() {
    this.archiveDir = path.join(app.getPath('userData'), 'temp-archives');
    this.ensureDirectoryExists();
  }
  
  async createArchive(files: string[], archiveId: string): Promise<string> {
    const archivePath = path.join(this.archiveDir, `${archiveId}.7z`);
    // 创建压缩文件
    await this.compress(files, archivePath);
    
    // 记录文件信息到数据库
    await this.recordArchiveFile(archiveId, archivePath);
    
    return archivePath;
  }
  
  async cleanupArchive(archiveId: string): Promise<void> {
    const archivePath = path.join(this.archiveDir, `${archiveId}.7z`);
    try {
      await fs.unlink(archivePath);
      await this.removeArchiveRecord(archiveId);
    } catch (error) {
      // 文件可能已被删除，忽略错误
    }
  }
}
```

#### 3.3 重新生成机制
当检测到临时文件丢失时，自动重新生成：

```typescript
async handleMissingArchive(task: UploadTask): Promise<void> {
  if (task.metadata?.originalFiles) {
    try {
      // 重新创建压缩文件
      const newArchivePath = await this.archiveManager.recreateArchive(
        task.metadata.originalFiles,
        task.id
      );
      
      // 更新任务文件路径
      task.filePath = newArchivePath;
      this.saveTaskToStore(task);
      
      tusLogger.info(`重新生成智能打包文件: ${task.fileName}`);
    } catch (error) {
      // 重新生成失败，标记为错误
      this.updateTaskStatus(task.id, "error", "无法重新生成压缩文件");
    }
  }
}
```

## 实现状态

### 已实现
- ✅ 临时文件存在性检查框架
- ✅ 错误状态标记机制
- ✅ 用户友好的错误提示

### 待实现
- ⏳ 主进程`checkFileExists` API
- ⏳ 持久化临时文件目录
- ⏳ 自动重新生成机制
- ⏳ 文件生命周期管理

## 用户体验优化

### 错误提示优化
```typescript
// 用户友好的错误信息
const errorMessages = {
  'temp-file-missing': '临时压缩文件已被清理，请重新选择文件上传',
  'temp-file-corrupted': '临时压缩文件已损坏，请重新选择文件上传',
  'temp-file-access-denied': '无法访问临时压缩文件，请检查文件权限'
};
```

### 自动恢复机制
```typescript
// 检测到文件丢失时的处理流程
async handleSmartPackingRecovery(task: UploadTask): Promise<void> {
  // 1. 显示恢复提示
  showRecoveryDialog({
    title: '检测到上传中断',
    message: '是否重新打包并继续上传？',
    actions: ['重新打包', '取消上传']
  });
  
  // 2. 用户选择重新打包
  if (userChoice === 'repack') {
    await this.recreateAndResumeUpload(task);
  }
}
```

## 监控和日志

### 关键指标
- 临时文件丢失频率
- 自动恢复成功率
- 用户重新上传率

### 日志记录
```typescript
// 临时文件状态日志
tusLogger.info(`智能打包文件状态检查: ${task.fileName}`, {
  taskId: task.id,
  filePath: task.filePath,
  fileExists: exists,
  fileSize: stats?.size,
  lastModified: stats?.mtime
});
```

## 最佳实践

### 1. 预防性措施
- 使用应用数据目录而非系统临时目录
- 定期清理过期的临时文件
- 在关键操作前检查文件状态

### 2. 恢复策略
- 提供自动重新生成选项
- 保存原始文件列表用于恢复
- 实现增量重试机制

### 3. 用户体验
- 清晰的错误提示信息
- 提供手动恢复选项
- 显示恢复进度

## 相关配置

### 临时文件配置
```typescript
interface TempFileConfig {
  // 临时文件目录
  tempDir: string;
  // 文件保留时间（小时）
  retentionHours: number;
  // 最大文件大小（字节）
  maxFileSize: number;
  // 清理策略
  cleanupStrategy: 'on-startup' | 'scheduled' | 'manual';
}
```

### 默认配置
```typescript
const defaultTempFileConfig: TempFileConfig = {
  tempDir: path.join(app.getPath('userData'), 'temp-archives'),
  retentionHours: 24,
  maxFileSize: 10 * 1024 * 1024 * 1024, // 10GB
  cleanupStrategy: 'on-startup'
};
```

## 总结

智能打包临时文件处理是一个复杂的问题，涉及文件系统、应用生命周期和用户体验等多个方面。当前的解决方案提供了基础的检查和错误处理机制，但完整的解决方案还需要实现持久化存储和自动恢复功能。

通过分阶段实现这些功能，可以逐步提升智能打包功能的稳定性和用户体验。
