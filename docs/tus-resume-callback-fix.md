# TUS断点续传回调修复文档

## 概述

本文档记录了TUS上传系统中断点续传回调不触发问题的完整修复过程。该问题主要表现为：当用户关闭Electron客户端后重新打开，断点续传任务虽然能够成功完成上传，但无法正确触发`startTask`回调函数。

## 问题分析

### 问题现象

1. **正常上传**：回调函数正常触发
2. **断点续传**：文件上传成功，但回调函数不触发
3. **智能打包断点续传**：除了回调问题外，还存在临时文件丢失的ENOENT错误

### 根本原因

通过深入分析主进程和渲染进程的代码，发现了以下根本原因：

#### 1. 主进程事件转发不完整
- **位置**：`electron/tus/index.ts:84-86`
- **问题**：`task-completed`事件只发送了`taskId`，没有发送完整的`task`对象
- **影响**：渲染进程无法获取完整的任务信息来触发回调

#### 2. 主进程任务恢复逻辑缺陷
- **位置**：`electron/tus/uploadManager.ts:746-763`
- **问题**：`restoreUnfinishedTasks`只恢复未完成任务，不恢复`completed`状态任务
- **影响**：断点续传完成时，主进程无法获取完整任务信息

#### 3. 渲染进程上传组丢失
- **位置**：`src/components/upload/composables/useTusUpload.ts`
- **问题**：客户端重启后，内存中的上传组(`uploadGroups`)被清空
- **影响**：断点续传完成时找不到对应的上传组，无法触发回调

## 修复方案

### 1. 修复主进程事件转发

**文件**：`electron/tus/index.ts`

```typescript
// 修复前
uploadManager.on("task-completed", (taskId: string) => {
  mainWindow?.webContents.send("upload-task-completed", taskId);
});

// 修复后
uploadManager.on("task-completed", (taskId: string) => {
  // 获取完整的任务对象并发送给渲染进程
  const task = uploadManager.getTask(taskId);
  mainWindow?.webContents.send("upload-task-completed", taskId, task);
});
```

**修复效果**：确保渲染进程能够接收到完整的任务对象。

### 2. 修复主进程任务恢复逻辑

**文件**：`electron/tus/uploadManager.ts`

```typescript
// 修复前
private restoreUnfinishedTasks(): void {
  const storedTasks = this.store.get("tasks", {}) as Record<string, UploadTask>;
  
  Object.values(storedTasks).forEach((storedTask) => {
    // 只恢复未完成的任务
    if (["uploading", "paused", "pending"].includes(storedTask.status)) {
      // ...
    }
  });
}

// 修复后
private restoreUnfinishedTasks(): void {
  const storedTasks = this.store.get("tasks", {}) as Record<string, UploadTask>;

  Object.values(storedTasks).forEach((storedTask) => {
    // 恢复所有任务到内存（包括已完成的任务，用于断点续传回调）
    if (["uploading", "paused", "pending", "completed"].includes(storedTask.status)) {
      if (storedTask.status === "uploading") {
        storedTask.status = "paused";
      }

      this.tasks.set(storedTask.id, {
        ...storedTask,
        startTime: new Date(storedTask.startTime),
      });
      
      tusLogger.info(`恢复任务: ${storedTask.fileName} (${storedTask.status})`);
    }
  });
  
  tusLogger.info(`已恢复 ${this.tasks.size} 个任务到内存`);
}
```

**修复效果**：确保主进程能够提供完整的任务信息给渲染进程。

### 3. 增强渲染进程断点续传处理

**文件**：`src/components/upload/composables/useTusUpload.ts`

#### 3.1 修复checkUploadGroupProgress返回值

```typescript
// 修复前
const checkUploadGroupProgress = (taskId: string, completedTask: UploadTask) => {
  // ... 处理逻辑
  break; // 找到后直接跳出
}

// 修复后
const checkUploadGroupProgress = (taskId: string, completedTask: UploadTask): boolean => {
  // ... 处理逻辑
  return true; // 找到了对应的上传组
  
  return false; // 没有找到对应的上传组
}
```

#### 3.2 增强handleTaskCompleted函数

```typescript
const handleTaskCompleted = async (taskId: string, taskFromMainProcess?: UploadTask) => {
  // 优先使用主进程传递的完整任务对象
  let completeTask = taskFromMainProcess;
  if (!completeTask) {
    completeTask = await ensureTaskCompleteness(taskId);
  }
  
  if (completeTask) {
    // ... 正常处理逻辑
    
    // 检查是否属于某个上传组，触发相应回调
    const foundGroup = checkUploadGroupProgress(taskId, completeTask);
    
    // 如果没有找到上传组，但任务有uploadGroupId，说明是断点续传
    if (!foundGroup && completeTask.metadata?.uploadGroupId) {
      // 创建临时上传组并触发回调
      const uploadGroupId = completeTask.metadata.uploadGroupId;
      const tempUploadGroup: UploadGroup = {
        id: uploadGroupId,
        files: [new File([], completeTask.fileName, { 
          type: completeTask.metadata?.fileType || 'application/octet-stream' 
        })],
        taskIds: [taskId],
        taskFileMap: new Map([[taskId, new File([], completeTask.fileName, { 
          type: completeTask.metadata?.fileType || 'application/octet-stream' 
        })]]),
        callbacks: {
          onFileUploaded: (file, _uploadTask) => {
            tusLogger.event(`断点续传文件上传完成回调触发: ${file.name}`);
          },
          onAllFilesUploaded: (files, _completedTasks) => {
            tusLogger.event(`断点续传所有文件上传完成回调触发: ${files.length} 个文件`);
          }
        },
        completedCount: 1,
        errorCount: 0,
        startTime: new Date(completeTask.startTime || Date.now()),
      };

      // 将临时上传组添加到内存中
      uploadGroups.value.set(uploadGroupId, tempUploadGroup);
      
      // 触发回调
      const callbackTriggered = checkUploadGroupProgress(taskId, completeTask);
      tusLogger.event(`断点续传回调触发结果: ${callbackTriggered ? '成功' : '失败'}`);
      
      // 清理临时上传组
      uploadGroups.value.delete(uploadGroupId);
    }
  }
};
```

**修复效果**：通过临时上传组机制，确保断点续传完成时能够正确触发回调。

## 技术特点

### 1. 临时上传组机制
- **目的**：为断点续传任务创建临时上传组，确保回调能够正常触发
- **生命周期**：创建 → 触发回调 → 立即清理
- **内存安全**：避免内存泄漏

### 2. 向后兼容性
- **正常上传**：完全不受影响，保持原有流程
- **断点续传**：新增处理逻辑，不破坏现有功能
- **智能打包**：支持智能打包的断点续传回调

### 3. 调试友好
- **详细日志**：每个关键步骤都有日志记录
- **状态跟踪**：可以清晰地跟踪回调触发过程
- **错误处理**：完善的异常处理机制

## 验证方法

### 1. 正常上传测试
```bash
# 测试步骤
1. 选择文件进行上传
2. 等待上传完成
3. 验证回调是否正常触发
4. 检查控制台日志

# 预期结果
- 回调正常触发
- 日志显示"正常上传任务完成，回调已触发"
```

### 2. 断点续传测试
```bash
# 测试步骤
1. 选择大文件开始上传
2. 上传进行到50%时关闭Electron客户端
3. 重新打开客户端
4. 等待任务自动恢复并完成上传
5. 检查控制台日志和回调触发情况

# 预期结果
- 主进程日志：显示"恢复任务: xxx (completed)"
- 渲染进程日志：显示"检测到断点续传任务完成"
- 回调触发日志：显示"断点续传回调触发结果: 成功"
```

### 3. 智能打包断点续传测试
```bash
# 测试步骤
1. 选择多个文件，触发智能打包上传
2. 上传进行到一半时关闭客户端
3. 重新打开客户端
4. 等待上传完成
5. 验证回调和临时文件处理

# 预期结果
- 智能打包任务能够正常恢复
- 回调正确触发
- 临时文件处理正常
```

## 调试信息

### 关键日志标识

#### 主进程日志
```
恢复任务: [文件名] (completed)
已恢复 [数量] 个任务到内存
```

#### 渲染进程日志
```
检测到断点续传任务完成: [文件名] (taskId: [任务ID])
断点续传回调触发结果: 成功/失败
断点续传任务处理完成: [文件名]
```

### 故障排查

如果断点续传回调仍然不触发，请检查：

1. **主进程是否正确恢复任务**
   - 查找日志：`恢复任务: xxx (completed)`
   - 如果没有，检查任务持久化逻辑

2. **渲染进程是否接收到完整任务对象**
   - 查找日志：`检测到断点续传任务完成`
   - 如果没有，检查事件转发逻辑

3. **临时上传组是否正确创建**
   - 查找日志：`断点续传回调触发结果`
   - 如果显示失败，检查上传组创建逻辑

## 相关文件

### 主要修改文件
- `electron/tus/index.ts` - 事件转发修复
- `electron/tus/uploadManager.ts` - 任务恢复逻辑修复
- `src/components/upload/composables/useTusUpload.ts` - 断点续传回调处理

### 相关文档
- `docs/tus-upload-system.md` - TUS上传系统整体架构
- `docs/smart-packing-feature.md` - 智能打包功能文档

## 版本信息

- **修复版本**：v1.2.0
- **修复日期**：2024-12-19
- **影响范围**：TUS上传系统断点续传功能
- **兼容性**：向后兼容，不影响现有功能

## 总结

本次修复通过以下三个层面的改进，彻底解决了断点续传回调不触发的问题：

1. **主进程层面**：修复事件转发和任务恢复逻辑
2. **渲染进程层面**：实现临时上传组机制
3. **调试层面**：增加详细的日志记录

修复后的系统能够正确处理各种断点续传场景，包括普通文件上传和智能打包上传，确保回调函数在所有情况下都能正确触发。
