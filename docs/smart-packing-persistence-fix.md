# 断点续传startTask调用修复文档

## 概述

本文档记录了断点续传完成时startTask调用问题的修复过程。该修复确保了断点续传完成后能够正确调用startTask API，将文件信息同步到后端系统。

**注意**：本文档之前包含的智能打包任务持久化修复已被还原，仅保留断点续传startTask调用的修复。

## 问题分析

### 断点续传startTask调用问题

**根本原因**：
- **uploadGroups内存清空**：应用重启后，内存中的`uploadGroups`被清空，导致断点续传完成时找不到对应的回调
- **参数签名不匹配**：`handleTaskCompleted`方法的参数签名与文档中的修复方案不匹配
- **关键参数缺失**：任务metadata中没有保存startTask所需的关键参数（categoryId、parentId、isNeedExtract等）
- **回调函数不完整**：断点续传的临时上传组回调只打印日志，没有实际调用startTask API

## 修复方案

### 断点续传startTask调用修复

#### 1. 修复handleTaskCompleted参数签名

**文件**：`src/components/Upload/composables/useTusUpload.ts`

```typescript
const handleTaskCompleted = async (taskId: string, taskFromMainProcess?: UploadTask) => {
  // 优先使用主进程传递的完整任务对象
  let completeTask = taskFromMainProcess;
  if (!completeTask) {
    completeTask = await ensureTaskCompleteness(taskId);
  }

  // ... 其余处理逻辑
};
```

#### 2. 保存startTask所需参数

**文件**：`src/components/Upload/UploadDialog.vue`

在创建上传任务时保存startTask所需的关键参数：

```typescript
// 🔧 修复：保存startTask所需的所有参数到metadata中
const metadata: Record<string, string> = {
  attributes: JSON.stringify(attributes.value),
  // 保存startTask所需的关键参数，用于断点续传
  categoryId: categoryId.value,
  parentId: parentId.value,
  isNeedExtract: isNeedExtract.value ? "1" : "0",
}
```

#### 3. 实现断点续传startTask调用

**文件**：`src/components/Upload/composables/useTusUpload.ts`

为断点续传的临时上传组添加能够调用startTask API的回调函数：

```typescript
onFileUploaded: async (file: File, uploadTask: UploadTask) => {
  // 从任务metadata中获取保存的参数
  const metadata = uploadTask.metadata || {};

  // 解析用户设置的属性
  let userAttributes: Record<string, any> = {};
  if (metadata.attributes) {
    userAttributes = JSON.parse(metadata.attributes);
  }

  const startTaskData = {
    upload_url: uploadTask.uploadUrl,
    category_id: metadata.categoryId || "",
    parent_id: metadata.parentId || "",
    relative_path: metadata.relativePath || file.name,
    is_folder: metadata.isFolder === "true" ? 1 : 0,
    is_need_extract: parseInt(metadata.isNeedExtract || "0"),
    ...userAttributes,
  };

  // 动态导入filesApi以避免循环依赖
  const { default: filesApi } = await import("@/api/services/files");
  const response = await filesApi.startTask(startTaskData);
},
```

## 技术特点

### 1. 参数持久化机制

- **关键参数保存**：在创建上传任务时将startTask所需的关键参数保存到metadata中
- **完整性保证**：确保断点续传时能够获取所有必要的参数
- **向后兼容**：不影响现有的上传功能

### 2. 回调触发优化

- **参数兼容性**：支持主进程传递完整任务对象
- **临时上传组**：为断点续传创建功能完整的临时上传组
- **API调用**：直接调用startTask API而不是仅仅打印日志

## 验证方法

### 断点续传startTask调用测试

```bash
# 测试步骤
1. 开始智能打包上传
2. 上传进行到一半时关闭应用
3. 重新打开应用
4. 等待断点续传完成
5. 检查控制台日志

# 预期结果
- 日志显示："断点续传回调触发结果: 成功"
- 日志显示："断点续传调用startTask: [文件名]"
- 日志显示："断点续传startTask成功: [文件名]"
- 文件信息成功同步到后端系统
```

## 相关文件

### 主要修改文件
- `src/components/Upload/UploadDialog.vue` - 保存startTask所需参数
- `src/components/Upload/composables/useTusUpload.ts` - 断点续传startTask调用实现

### 相关文档
- `docs/tus-resume-callback-fix.md` - 断点续传回调修复文档

## 版本信息

- **修复版本**：v1.2.2
- **修复日期**：2024-12-19
- **影响范围**：断点续传功能
- **兼容性**：向后兼容，不影响现有功能

## 还原说明

本文档之前包含的智能打包任务持久化修复已被还原，原因是：
1. 智能打包占位任务机制可能引入新的复杂性
2. 需要保持智能打包功能的稳定性
3. 仅保留已确认工作的断点续传startTask调用修复

## 总结

本次修复通过参数保存和API调用修复解决了断点续传startTask调用问题。修复后的系统能够：

1. **保证startTask调用**：断点续传完成时能够正确调用startTask API同步文件信息
2. **参数完整性**：保存并恢复startTask所需的所有关键参数
3. **向后兼容**：不影响现有的上传和智能打包功能
4. **维护稳定性**：避免引入可能影响智能打包功能的复杂修改

这个修复确保了断点续传后文件能够正确同步到后端系统，解决了用户报告的核心问题。
