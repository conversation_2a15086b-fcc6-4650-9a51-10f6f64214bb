# 智能打包任务持久化和回调修复文档

## 概述

本文档记录了智能打包上传任务持久化问题和任务恢复时startTask调用问题的完整修复过程。这些修复确保了智能打包任务在应用重启后能够正确持久化，并且断点续传完成时能够正确触发回调。

## 问题分析

### 1. 智能打包任务持久化问题

**根本原因**：
- **异步创建时机问题**：智能打包任务采用两阶段创建模式（压缩→上传），真正的上传任务只有在压缩完成后才会被创建和保存到localStorage
- **压缩阶段无持久化**：如果用户在压缩阶段关闭应用，压缩任务本身不会被保存，导致整个智能打包流程丢失
- **时间窗口问题**：从开始智能打包到创建真正的上传任务之间存在时间窗口，在此期间关闭应用会导致任务完全丢失

### 2. 任务恢复时startTask调用问题

**根本原因**：
- **uploadGroups内存清空**：应用重启后，内存中的`uploadGroups`被清空，导致断点续传完成时找不到对应的回调
- **参数签名不匹配**：`handleTaskCompleted`方法的参数签名与文档中的修复方案不匹配
- **关键参数缺失**：任务metadata中没有保存startTask所需的关键参数（categoryId、parentId、isNeedExtract等）
- **回调函数不完整**：断点续传的临时上传组回调只打印日志，没有实际调用startTask API

## 修复方案

### 1. 智能打包任务持久化修复

**核心思路**：在开始智能打包时立即创建占位上传任务

#### 1.1 创建占位任务

**文件**：`electron/tus/uploadManager.ts`

```typescript
// 🔧 修复：立即创建占位上传任务以确保持久化
const placeholderTaskId = this.generateTaskId();
const placeholderTask: UploadTask = {
  id: placeholderTaskId,
  filePath: "", // 临时为空，压缩完成后更新
  fileName: `${archiveName}.7z`,
  fileSize: 0, // 临时为0，压缩完成后更新
  progress: 0,
  status: "pending",
  bytesUploaded: 0,
  uploadSpeed: 0,
  remainingTime: 0,
  startTime: new Date(),
  metadata: {
    ...this.config.metadata,
    ...options?.metadata,
    uploadType: "smart-packed-archive",
    originalFileCount: filePaths.length.toString(),
    packingReason: analysis.reason,
    smartPackingPhase: "compressing", // 标记当前处于压缩阶段
  },
  resumable: true,
  isFolder: false,
  isEmpty: false,
};

// 保存占位任务到内存和存储
this.tasks.set(placeholderTaskId, placeholderTask);
this.saveTaskToStore(placeholderTask);
this.emit("task-created", placeholderTaskId, placeholderTask);
```

#### 1.2 更新占位任务

压缩完成后，更新占位任务而不是创建新任务：

```typescript
// 🔧 修复：更新占位任务而不是创建新任务
const placeholderTask = this.tasks.get(placeholderTaskId);
if (placeholderTask) {
  // 更新占位任务的文件路径和大小
  placeholderTask.filePath = result.archivePath;
  placeholderTask.fileSize = result.stats?.compressedSize || 0;
  placeholderTask.metadata = {
    ...placeholderTask.metadata,
    compressionStats: JSON.stringify(result.stats),
    smartPackingPhase: "uploading", // 更新阶段为上传
  };
  
  // 保存更新后的任务
  this.saveTaskToStore(placeholderTask);
  
  // 开始上传占位任务
  await this.startUpload(placeholderTaskId);
}
```

#### 1.3 恢复时的特殊处理

**文件**：`src/components/Upload/composables/useTusUpload.ts`

```typescript
// 检查智能打包任务的特殊处理
if (task.metadata?.uploadType === "smart-packed-archive") {
  // 检查是否处于压缩阶段
  if (task.metadata?.smartPackingPhase === "compressing") {
    // 压缩阶段的任务在应用重启后无法恢复，标记为错误
    const updatedTask = {
      ...task,
      status: "error" as UploadStatus,
      error: "压缩过程中断，请重新上传",
    };
    tasks.value.set(task.id, updatedTask);
    continue;
  }
}
```

### 2. 任务恢复时startTask调用修复

#### 2.1 修复handleTaskCompleted参数签名

**文件**：`src/components/Upload/composables/useTusUpload.ts`

```typescript
const handleTaskCompleted = async (taskId: string, taskFromMainProcess?: UploadTask) => {
  // 优先使用主进程传递的完整任务对象
  let completeTask = taskFromMainProcess;
  if (!completeTask) {
    completeTask = await ensureTaskCompleteness(taskId);
  }
  
  // ... 其余处理逻辑
};
```

#### 2.2 保存startTask所需参数

**文件**：`src/components/Upload/UploadDialog.vue`

在创建上传任务时保存startTask所需的关键参数：

```typescript
// 🔧 修复：保存startTask所需的所有参数到metadata中
const metadata: Record<string, string> = {
  attributes: JSON.stringify(attributes.value),
  // 保存startTask所需的关键参数，用于断点续传
  categoryId: categoryId.value,
  parentId: parentId.value,
  isNeedExtract: isNeedExtract.value ? "1" : "0",
}
```

#### 2.3 实现断点续传startTask调用

**文件**：`src/components/Upload/composables/useTusUpload.ts`

为断点续传的临时上传组添加能够调用startTask API的回调函数：

```typescript
onFileUploaded: async (file: File, uploadTask: UploadTask) => {
  // 从任务metadata中获取保存的参数
  const metadata = uploadTask.metadata || {};

  // 解析用户设置的属性
  let userAttributes: Record<string, any> = {};
  if (metadata.attributes) {
    userAttributes = JSON.parse(metadata.attributes);
  }

  const startTaskData = {
    upload_url: uploadTask.uploadUrl,
    category_id: metadata.categoryId || "",
    parent_id: metadata.parentId || "",
    relative_path: metadata.relativePath || file.name,
    is_folder: metadata.isFolder === "true" ? 1 : 0,
    is_need_extract: parseInt(metadata.isNeedExtract || "0"),
    ...userAttributes,
  };

  // 动态导入filesApi以避免循环依赖
  const { default: filesApi } = await import("@/api/services/files");
  const response = await filesApi.startTask(startTaskData);
},
```

## 技术特点

### 1. 占位任务机制

- **立即持久化**：智能打包开始时立即创建并保存占位任务
- **阶段标记**：使用`smartPackingPhase`字段标记当前阶段（compressing/uploading）
- **原地更新**：压缩完成后更新占位任务而不是创建新任务

### 2. 错误恢复机制

- **压缩阶段检测**：恢复时检测压缩阶段的任务并标记为错误
- **文件存在性检查**：上传阶段的任务检查临时文件是否存在
- **用户友好提示**：提供清晰的错误信息指导用户操作

### 3. 回调触发优化

- **参数兼容性**：支持主进程传递完整任务对象
- **临时上传组**：为断点续传创建功能完整的临时上传组
- **回调保证**：确保所有场景下都能正确触发回调

## 验证方法

### 1. 智能打包持久化测试

```bash
# 测试步骤
1. 选择大量文件触发智能打包
2. 在压缩阶段关闭应用
3. 重新打开应用
4. 检查任务列表中是否有错误状态的占位任务

# 预期结果
- 占位任务显示为错误状态
- 错误信息："压缩过程中断，请重新上传"
```

### 2. 断点续传回调测试

```bash
# 测试步骤
1. 开始智能打包上传
2. 上传进行到一半时关闭应用
3. 重新打开应用
4. 等待断点续传完成
5. 检查控制台日志

# 预期结果
- 日志显示："断点续传回调触发结果: 成功"
- 回调函数正确执行
```

## 相关文件

### 主要修改文件
- `electron/tus/uploadManager.ts` - 占位任务创建和管理
- `src/components/Upload/composables/useTusUpload.ts` - 回调修复和恢复处理

### 相关文档
- `docs/tus-resume-callback-fix.md` - 断点续传回调修复文档
- `docs/smart-packing-temp-file-handling.md` - 智能打包临时文件处理方案

## 版本信息

- **修复版本**：v1.2.1
- **修复日期**：2024-12-19
- **影响范围**：智能打包上传功能
- **兼容性**：向后兼容，不影响现有功能

## 总结

本次修复通过占位任务机制解决了智能打包任务持久化问题，通过参数保存和API调用修复解决了断点续传startTask调用问题。修复后的系统能够：

1. **确保任务持久化**：智能打包任务在任何阶段都能正确持久化
2. **提供错误恢复**：压缩中断的任务能够被正确识别和处理
3. **保证startTask调用**：断点续传完成时能够正确调用startTask API同步文件信息
4. **参数完整性**：保存并恢复startTask所需的所有关键参数
5. **维护用户体验**：提供清晰的错误提示和恢复指导

这些改进显著提升了智能打包功能的稳定性和用户体验，确保断点续传后文件能够正确同步到后端系统。
